{"cik": "0001766379", "entityType": "other", "sic": "", "sicDescription": "", "ownerOrg": "", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 0, "name": "Sky Holdco Corp", "tickers": [], "exchanges": [], "ein": "833048052", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "", "fiscalYearEnd": "1231", "stateOfIncorporation": "DE", "stateOfIncorporationDescription": "DE", "addresses": {"mailing": {"street1": "C/O PERMIRA ADVISERS LLC", "street2": "320 PARK AVENUE, 23RD FLOOR", "city": "NEW YORK", "stateOrCountry": "NY", "zipCode": "10022", "stateOrCountryDescription": "NY", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "C/O PERMIRA ADVISERS LLC", "street2": "320 PARK AVENUE, 23RD FLOOR", "city": "NEW YORK", "stateOrCountry": "NY", "zipCode": "10022", "stateOrCountryDescription": "NY", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "(*************", "flags": "", "formerNames": [], "filings": {"recent": {"accessionNumber": ["0000950172-25-000121", "0000950172-25-000118", "0000950172-25-000114", "0000950172-25-000111", "0000950172-25-000083", "0000950172-25-000003", "0000950172-24-000462", "0000950172-24-000457", "0000950172-24-000454", "0000950172-24-000422", "0000950172-24-000356", "0000950172-24-000351", "0000950172-24-000342", "0000950172-24-000314", "0000950172-24-000178", "0000950172-24-000012", "0000950172-23-000168", "0000950172-23-000161", "0000950172-23-000145", "0000950172-23-000142", "0000950172-23-000118", "0000950172-23-000098", "0000950172-23-000096", "0000950172-23-000089", "0000950172-23-000055", "0000950172-23-000014", "0000950172-22-000167", "0000950172-22-000160", "0000950172-22-000158", "0000950172-22-000157", "0000950172-22-000134", "0000950172-22-000101", "0000950172-22-000098", "0000950172-22-000093", "0000950172-22-000064", "0000950172-22-000014", "0000950172-21-000153", "0000950172-21-000139", "0000950172-21-000135", "0000950172-21-000134", "0000950172-21-000133", "0000950172-21-000087", "0000950172-21-000080", "0000950172-21-000049", "0000950172-21-000007", "0000892712-20-000346", "0000950172-20-000148", "0000950172-20-000144", "0000950172-20-000143", "0000950172-20-000141", "0000950172-20-000102", "0000950172-20-000100", "0000950172-19-000138", "0000950172-19-000130", "0000950172-19-000128", "0000950172-19-000088", "0000950172-19-000034", "0000950172-19-000028"], "filingDate": ["2025-06-24", "2025-06-16", "2025-06-09", "2025-05-30", "2025-04-07", "2025-01-14", "2024-12-06", "2024-11-12", "2024-11-08", "2024-09-13", "2024-06-24", "2024-06-14", "2024-06-10", "2024-05-30", "2024-04-05", "2024-01-16", "2023-12-06", "2023-11-20", "2023-11-09", "2023-11-08", "2023-09-14", "2023-06-23", "2023-06-21", "2023-06-09", "2023-04-07", "2023-01-19", "2022-12-06", "2022-11-21", "2022-11-14", "2022-11-08", "2022-09-14", "2022-06-24", "2022-06-22", "2022-06-09", "2022-04-07", "2022-01-14", "2021-12-06", "2021-11-19", "2021-11-10", "2021-11-08", "2021-11-08", "2021-06-23", "2021-06-14", "2021-04-08", "2021-01-14", "2020-12-14", "2020-12-04", "2020-11-19", "2020-11-19", "2020-11-06", "2020-06-23", "2020-06-12", "2019-12-06", "2019-11-19", "2019-11-08", "2019-06-14", "2019-02-21", "2019-01-30"], "reportDate": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "acceptanceDateTime": ["2025-06-24T14:33:54.000Z", "2025-06-16T21:20:10.000Z", "2025-06-09T21:26:56.000Z", "2025-05-30T21:28:39.000Z", "2025-04-07T16:31:30.000Z", "2025-01-14T18:59:46.000Z", "2024-12-06T15:21:46.000Z", "2024-11-12T17:55:58.000Z", "2024-11-08T17:07:20.000Z", "2024-09-13T13:20:24.000Z", "2024-06-24T20:27:40.000Z", "2024-06-14T14:43:21.000Z", "2024-06-10T15:08:59.000Z", "2024-05-30T15:56:18.000Z", "2024-04-05T15:15:04.000Z", "2024-01-16T21:41:47.000Z", "2023-12-06T16:21:22.000Z", "2023-11-20T17:13:10.000Z", "2023-11-09T17:12:37.000Z", "2023-11-08T13:54:00.000Z", "2023-09-14T17:03:00.000Z", "2023-06-23T15:26:21.000Z", "2023-06-21T16:12:50.000Z", "2023-06-09T19:58:16.000Z", "2023-04-07T16:22:41.000Z", "2023-01-19T15:49:37.000Z", "2022-12-06T18:35:48.000Z", "2022-11-21T20:49:28.000Z", "2022-11-11T01:19:06.000Z", "2022-11-08T22:23:07.000Z", "2022-09-14T18:46:24.000Z", "2022-06-24T16:08:22.000Z", "2022-06-22T00:02:18.000Z", "2022-06-09T20:14:13.000Z", "2022-04-07T15:04:20.000Z", "2022-01-14T16:03:11.000Z", "2021-12-06T20:47:39.000Z", "2021-11-19T17:21:57.000Z", "2021-11-10T18:41:55.000Z", "2021-11-08T16:46:39.000Z", "2021-11-08T16:44:57.000Z", "2021-06-23T15:20:49.000Z", "2021-06-11T22:51:44.000Z", "2021-04-07T23:10:24.000Z", "2021-01-14T17:14:22.000Z", "2020-12-14T15:49:52.000Z", "2020-12-04T13:23:39.000Z", "2020-11-19T19:42:26.000Z", "2020-11-19T19:25:39.000Z", "2020-11-06T13:24:53.000Z", "2020-06-23T14:31:38.000Z", "2020-06-12T16:45:02.000Z", "2019-12-06T18:38:11.000Z", "2019-11-19T17:47:18.000Z", "2019-11-08T16:17:05.000Z", "2019-06-14T17:26:29.000Z", "2019-02-21T18:37:00.000Z", "2019-01-30T18:17:36.000Z"], "act": ["33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33"], "form": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D", "D", "D", "D/A", "D/A", "D", "D/A", "D", "D/A", "D", "D", "D", "D", "D", "D"], "fileNumber": ["021-369465", "021-341972", "021-448705", "021-514618", "021-395023", "021-354985", "021-354985", "021-420244", "021-352942", "021-458875", "021-369465", "021-341972", "021-448705", "021-514618", "021-395023", "021-386351", "021-354985", "021-353711", "021-420244", "021-352942", "021-458875", "021-369465", "021-341972", "021-448705", "021-395023", "021-386351", "021-354985", "021-353711", "021-420244", "021-352942", "021-458875", "021-369465", "021-341972", "021-448705", "021-395023", "021-386351", "021-354985", "021-353711", "021-420244", "021-420244", "021-352942", "021-369465", "021-341972", "021-395023", "021-386351", "021-383082", "021-354985", "021-353711", "021-381026", "021-352942", "021-369465", "021-341972", "021-354985", "021-353711", "021-352942", "021-341972", "021-333272", "021-331733"], "filmNumber": ["251068243", "251051132", "251035165", "251012393", "25817154", "25528817", "241530882", "241446307", "241438963", "241296594", "241064668", "241043463", "241031800", "241002637", "24825339", "24535684", "231468744", "231421734", "231391307", "231386185", "231254598", "231035885", "231028639", "231005100", "23808284", "23536317", "221447276", "221405648", "221379241", "221370008", "221242813", "221038816", "221029648", "221006119", "22812715", "22530813", "211473123", "211427460", "211395827", "211387038", "211387034", "211037596", "211012746", "21813443", "21528083", "201385403", "201368757", "201328467", "201328415", "201292503", "20980679", "20959610", "191272658", "191230138", "191202932", "19898264", "19621187", "19551124"], "items": ["06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b"], "core_type": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D", "D", "D", "D/A", "D/A", "D", "D/A", "D", "D/A", "D", "D", "D", "D", "D", "D"], "size": [8699, 8703, 8699, 8699, 8695, 9535, 9583, 9584, 9535, 9536, 9537, 9537, 9537, 9442, 10362, 10419, 10414, 10366, 10415, 10366, 10368, 10368, 10368, 10368, 10306, 10356, 10416, 10366, 10415, 10366, 10273, 11196, 11196, 11101, 10243, 10245, 10353, 10303, 10358, 10216, 10303, 10311, 10268, 10156, 9329, 9300, 9526, 9476, 9381, 9476, 9392, 9446, 9431, 9381, 9381, 9351, 8542, 8583], "isXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml"], "primaryDocDescription": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "files": []}}