#!/usr/bin/env python3
"""
Memory Monitor Utility

Provides memory monitoring and leak detection for the SEC Form D analysis pipeline.
Enhanced version with 40GB support, automatic garbage collection, and MLX model cache flushing.
"""

import gc
import logging
import time
import resource
import os
import threading
import psutil
from typing import Dict, Any, Optional, Callable
from functools import wraps
import tracemalloc

class MemoryMonitor:
    """
    Memory monitoring and leak detection utility.
    """
    
    def __init__(self, enable_tracemalloc: bool = True):
        """
        Initialize memory monitor.

        Args:
            enable_tracemalloc: Whether to enable detailed memory tracing
        """
        self.baseline_memory = None
        self.checkpoints = {}

        if enable_tracemalloc:
            tracemalloc.start()
            self.tracemalloc_enabled = True
        else:
            self.tracemalloc_enabled = False
    
    def get_memory_info(self) -> Dict[str, Any]:
        """
        Get current memory usage information using built-in Python modules.

        Returns:
            Dictionary with memory statistics
        """
        try:
            # Use resource module for memory info (Unix/macOS)
            import resource
            import platform
            memory_usage = resource.getrusage(resource.RUSAGE_SELF)

            # macOS reports in bytes, Linux in KB
            if platform.system() == 'Darwin':  # macOS
                rss_mb = memory_usage.ru_maxrss / 1024 / 1024  # Convert bytes to MB
            else:  # Linux
                rss_mb = memory_usage.ru_maxrss / 1024  # Convert KB to MB

            info = {
                'rss_mb': rss_mb,
                'percent': 0.0,  # Not available without psutil
                'available_gb': 0.0,  # Not available without psutil
                'total_gb': 0.0,  # Not available without psutil
                'system_percent': 0.0  # Not available without psutil
            }
        except:
            # Fallback to basic info
            info = {
                'rss_mb': 0.0,
                'percent': 0.0,
                'available_gb': 0.0,
                'total_gb': 0.0,
                'system_percent': 0.0
            }

        if self.tracemalloc_enabled:
            current, peak = tracemalloc.get_traced_memory()
            info['traced_current_mb'] = current / 1024 / 1024
            info['traced_peak_mb'] = peak / 1024 / 1024

        return info
    
    def set_baseline(self) -> None:
        """Set the baseline memory usage."""
        self.baseline_memory = self.get_memory_info()
        logging.info(f"Memory baseline set: {self.baseline_memory['rss_mb']:.1f} MB RSS")
    
    def checkpoint(self, name: str) -> Dict[str, Any]:
        """
        Create a memory checkpoint.
        
        Args:
            name: Name of the checkpoint
            
        Returns:
            Memory info at checkpoint
        """
        info = self.get_memory_info()
        self.checkpoints[name] = info
        
        if self.baseline_memory:
            delta_mb = info['rss_mb'] - self.baseline_memory['rss_mb']
            logging.info(f"Memory checkpoint '{name}': {info['rss_mb']:.1f} MB RSS (+{delta_mb:.1f} MB)")
        else:
            logging.info(f"Memory checkpoint '{name}': {info['rss_mb']:.1f} MB RSS")
        
        return info
    
    def check_memory_limit(self, limit_gb: float = 8.0) -> bool:
        """
        Check if memory usage exceeds limit.
        
        Args:
            limit_gb: Memory limit in GB
            
        Returns:
            True if under limit, False if over
        """
        info = self.get_memory_info()
        current_gb = info['rss_mb'] / 1024
        
        if current_gb > limit_gb:
            logging.error(f"Memory limit exceeded: {current_gb:.1f} GB > {limit_gb:.1f} GB")
            return False
        
        return True
    
    def force_cleanup(self) -> Dict[str, Any]:
        """
        Force garbage collection and return memory info.
        
        Returns:
            Memory info after cleanup
        """
        before = self.get_memory_info()
        
        # Force garbage collection
        collected = gc.collect()
        
        after = self.get_memory_info()
        freed_mb = before['rss_mb'] - after['rss_mb']
        
        logging.info(f"Garbage collection: freed {freed_mb:.1f} MB, collected {collected} objects")
        
        return {
            'before': before,
            'after': after,
            'freed_mb': freed_mb,
            'collected_objects': collected
        }
    
    def get_top_memory_objects(self, limit: int = 10) -> Optional[list]:
        """
        Get top memory-consuming objects (requires tracemalloc).
        
        Args:
            limit: Number of top objects to return
            
        Returns:
            List of top memory objects or None if tracemalloc disabled
        """
        if not self.tracemalloc_enabled:
            return None
        
        snapshot = tracemalloc.take_snapshot()
        top_stats = snapshot.statistics('lineno')
        
        return [
            {
                'filename': stat.traceback.format()[0],
                'size_mb': stat.size / 1024 / 1024,
                'count': stat.count
            }
            for stat in top_stats[:limit]
        ]

def memory_monitor(checkpoint_name: str = None, memory_limit_gb: float = 8.0):
    """
    Decorator to monitor memory usage of functions.
    
    Args:
        checkpoint_name: Name for memory checkpoint
        memory_limit_gb: Memory limit in GB
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            monitor = MemoryMonitor()
            
            # Set checkpoint name
            name = checkpoint_name or f"{func.__name__}"
            
            # Before execution
            monitor.checkpoint(f"{name}_start")
            
            try:
                # Execute function
                result = func(*args, **kwargs)
                
                # After execution
                monitor.checkpoint(f"{name}_end")
                
                # Check memory limit
                if not monitor.check_memory_limit(memory_limit_gb):
                    logging.warning(f"Memory limit exceeded in {name}")
                    monitor.force_cleanup()
                
                return result
                
            except Exception as e:
                # On error, still check memory
                monitor.checkpoint(f"{name}_error")
                monitor.force_cleanup()
                raise
        
        return wrapper
    return decorator

class EnhancedMemoryMonitor:
    """
    Enhanced memory monitor with 40GB support, automatic garbage collection,
    and MLX model cache flushing capabilities.
    """

    def __init__(self,
                 memory_limit_gb: float = 40.0,
                 warning_threshold_gb: float = 30.0,
                 critical_threshold_gb: float = 35.0,
                 enable_auto_gc: bool = True,
                 enable_mlx_cache_flush: bool = True):
        """
        Initialize enhanced memory monitor.

        Args:
            memory_limit_gb: Maximum memory limit in GB
            warning_threshold_gb: Warning threshold in GB
            critical_threshold_gb: Critical threshold in GB
            enable_auto_gc: Enable automatic garbage collection
            enable_mlx_cache_flush: Enable MLX model cache flushing
        """
        self.memory_limit_gb = memory_limit_gb
        self.warning_threshold_gb = warning_threshold_gb
        self.critical_threshold_gb = critical_threshold_gb
        self.enable_auto_gc = enable_auto_gc
        self.enable_mlx_cache_flush = enable_mlx_cache_flush

        # Monitoring state
        self.process = psutil.Process()
        self.baseline_memory = None
        self.checkpoints = {}
        self.warning_count = 0
        self.critical_count = 0
        self.gc_count = 0
        self.cache_flush_count = 0

        # Thread safety
        self._lock = threading.Lock()

        # Initialize tracemalloc
        if not tracemalloc.is_tracing():
            tracemalloc.start()

        logger = logging.getLogger(__name__)
        logger.info(f"Enhanced Memory Monitor initialized: limit={memory_limit_gb}GB, "
                   f"warning={warning_threshold_gb}GB, critical={critical_threshold_gb}GB")

    def get_memory_usage_gb(self) -> float:
        """Get current memory usage in GB."""
        try:
            memory_info = self.process.memory_info()
            return memory_info.rss / (1024 ** 3)  # Convert bytes to GB
        except Exception as e:
            logging.warning(f"Failed to get memory usage: {e}")
            return 0.0

    def get_memory_percent(self) -> float:
        """Get current memory usage as percentage of system memory."""
        try:
            return self.process.memory_percent()
        except Exception as e:
            logging.warning(f"Failed to get memory percentage: {e}")
            return 0.0

    def check_memory_status(self) -> Dict[str, Any]:
        """
        Check current memory status and return detailed information.

        Returns:
            Dictionary with memory status information
        """
        with self._lock:
            current_memory_gb = self.get_memory_usage_gb()
            memory_percent = self.get_memory_percent()

            status = {
                "current_memory_gb": current_memory_gb,
                "memory_limit_gb": self.memory_limit_gb,
                "memory_percent": memory_percent,
                "warning_threshold_gb": self.warning_threshold_gb,
                "critical_threshold_gb": self.critical_threshold_gb,
                "is_warning": current_memory_gb >= self.warning_threshold_gb,
                "is_critical": current_memory_gb >= self.critical_threshold_gb,
                "is_over_limit": current_memory_gb >= self.memory_limit_gb,
                "available_memory_gb": self.memory_limit_gb - current_memory_gb,
                "warning_count": self.warning_count,
                "critical_count": self.critical_count,
                "gc_count": self.gc_count,
                "cache_flush_count": self.cache_flush_count
            }

            return status

    def handle_memory_pressure(self, force: bool = False) -> bool:
        """
        Handle memory pressure by performing cleanup operations.

        Args:
            force: Force cleanup regardless of thresholds

        Returns:
            True if cleanup was performed, False otherwise
        """
        status = self.check_memory_status()
        cleanup_performed = False

        if force or status["is_warning"]:
            logger = logging.getLogger(__name__)

            if status["is_warning"]:
                self.warning_count += 1
                logger.warning(f"Memory warning: {status['current_memory_gb']:.2f}GB "
                             f"(threshold: {self.warning_threshold_gb}GB)")

            if status["is_critical"]:
                self.critical_count += 1
                logger.error(f"Memory critical: {status['current_memory_gb']:.2f}GB "
                           f"(threshold: {self.critical_threshold_gb}GB)")

            # Perform garbage collection
            if self.enable_auto_gc:
                logger.info("Performing automatic garbage collection...")
                collected = gc.collect()
                self.gc_count += 1
                logger.info(f"Garbage collection completed: {collected} objects collected")
                cleanup_performed = True

            # Flush MLX model cache if critical
            if self.enable_mlx_cache_flush and (status["is_critical"] or force):
                try:
                    self._flush_mlx_cache()
                    cleanup_performed = True
                except Exception as e:
                    logger.warning(f"Failed to flush MLX cache: {e}")

            # Log post-cleanup status
            if cleanup_performed:
                new_status = self.check_memory_status()
                memory_freed = status["current_memory_gb"] - new_status["current_memory_gb"]
                logger.info(f"Memory cleanup completed: freed {memory_freed:.2f}GB, "
                          f"current usage: {new_status['current_memory_gb']:.2f}GB")

        return cleanup_performed

    def _flush_mlx_cache(self):
        """Flush MLX model cache to free memory."""
        logger = logging.getLogger(__name__)
        logger.info("Flushing MLX model cache...")

        try:
            # Import MLX and flush cache
            import mlx.core as mx
            mx.metal.clear_cache()
            self.cache_flush_count += 1
            logger.info("MLX cache flushed successfully")
        except ImportError:
            logger.warning("MLX not available for cache flushing")
        except Exception as e:
            logger.error(f"Error flushing MLX cache: {e}")

    def checkpoint(self, name: str) -> Dict[str, Any]:
        """
        Create a memory checkpoint and handle any memory pressure.

        Args:
            name: Checkpoint name

        Returns:
            Memory status at checkpoint
        """
        with self._lock:
            status = self.check_memory_status()
            self.checkpoints[name] = {
                "timestamp": time.time(),
                "memory_gb": status["current_memory_gb"],
                "memory_percent": status["memory_percent"]
            }

            # Handle memory pressure automatically
            self.handle_memory_pressure()

            return status

    def get_checkpoint_diff(self, start_checkpoint: str, end_checkpoint: str) -> Optional[Dict[str, Any]]:
        """
        Get memory difference between two checkpoints.

        Args:
            start_checkpoint: Starting checkpoint name
            end_checkpoint: Ending checkpoint name

        Returns:
            Memory difference information or None if checkpoints not found
        """
        if start_checkpoint not in self.checkpoints or end_checkpoint not in self.checkpoints:
            return None

        start = self.checkpoints[start_checkpoint]
        end = self.checkpoints[end_checkpoint]

        return {
            "memory_diff_gb": end["memory_gb"] - start["memory_gb"],
            "time_diff_seconds": end["timestamp"] - start["timestamp"],
            "start_memory_gb": start["memory_gb"],
            "end_memory_gb": end["memory_gb"]
        }

# Global memory monitor instance
global_monitor = MemoryMonitor()

# Global enhanced memory monitor instance
enhanced_monitor = EnhancedMemoryMonitor()

def enhanced_memory_monitor(checkpoint_name: str = None,
                          memory_limit_gb: float = 40.0,
                          auto_cleanup: bool = True):
    """
    Enhanced decorator to monitor memory usage with automatic cleanup.

    Args:
        checkpoint_name: Name for memory checkpoint
        memory_limit_gb: Memory limit in GB
        auto_cleanup: Enable automatic cleanup on memory pressure
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Use global enhanced monitor or create new one
            monitor = enhanced_monitor
            if monitor.memory_limit_gb != memory_limit_gb:
                monitor = EnhancedMemoryMonitor(memory_limit_gb=memory_limit_gb)

            # Set checkpoint name
            name = checkpoint_name or f"{func.__name__}"

            # Before execution
            start_status = monitor.checkpoint(f"{name}_start")
            logger = logging.getLogger(__name__)
            logger.info(f"Starting {name}: {start_status['current_memory_gb']:.2f}GB memory usage")

            try:
                # Execute function
                result = func(*args, **kwargs)

                # After execution
                end_status = monitor.checkpoint(f"{name}_end")

                # Calculate memory difference
                diff = monitor.get_checkpoint_diff(f"{name}_start", f"{name}_end")
                if diff:
                    logger.info(f"Completed {name}: {end_status['current_memory_gb']:.2f}GB memory usage "
                              f"(diff: {diff['memory_diff_gb']:+.2f}GB)")

                # Handle memory pressure if needed
                if auto_cleanup and end_status["is_warning"]:
                    monitor.handle_memory_pressure()

                return result

            except Exception as e:
                # On error, still check memory and cleanup
                error_status = monitor.checkpoint(f"{name}_error")
                logger.error(f"Error in {name}: {error_status['current_memory_gb']:.2f}GB memory usage")

                if auto_cleanup:
                    monitor.handle_memory_pressure(force=True)

                raise

        return wrapper
    return decorator
