#!/usr/bin/env python3
"""
Adaptive Batch Processor for SEC Form D Analysis

Implements intelligent batch processing with:
- Dynamic batch sizing based on memory usage
- Concurrent processing capabilities
- Memory-aware filing complexity assessment
- Automatic resource adjustment
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
import threading

logger = logging.getLogger(__name__)

class AdaptiveBatchProcessor:
    """
    Adaptive batch processor that dynamically adjusts batch sizes based on system resources.
    """
    
    def __init__(self, 
                 memory_monitor=None,
                 initial_batch_size: int = 5,
                 min_batch_size: int = 1,
                 max_batch_size: int = 20,
                 memory_per_filing_gb: float = 0.5,
                 max_workers: int = 4):
        """
        Initialize the adaptive batch processor.
        
        Args:
            memory_monitor: Enhanced memory monitor instance
            initial_batch_size: Starting batch size
            min_batch_size: Minimum allowed batch size
            max_batch_size: Maximum allowed batch size
            memory_per_filing_gb: Estimated memory usage per filing in GB
            max_workers: Maximum number of worker threads
        """
        self.memory_monitor = memory_monitor
        self.initial_batch_size = initial_batch_size
        self.min_batch_size = min_batch_size
        self.max_batch_size = max_batch_size
        self.memory_per_filing_gb = memory_per_filing_gb
        self.max_workers = max_workers
        
        # Current batch size (adaptive)
        self.current_batch_size = initial_batch_size
        
        # Performance tracking
        self.processing_stats = {
            "batches_processed": 0,
            "total_filings": 0,
            "total_processing_time": 0.0,
            "avg_batch_time": 0.0,
            "avg_filing_time": 0.0,
            "memory_pressure_adjustments": 0,
            "complexity_adjustments": 0
        }
        
        # Thread safety
        self._lock = threading.Lock()
        
        logger.info(f"Adaptive Batch Processor initialized: batch_size={initial_batch_size}, "
                   f"memory_per_filing={memory_per_filing_gb}GB, max_workers={max_workers}")
    
    def assess_filing_complexity(self, filing_entry: Dict[str, Any]) -> float:
        """
        Assess the complexity of a filing for memory estimation.
        
        Args:
            filing_entry: Filing entry to assess
            
        Returns:
            Complexity score (1.0 = normal, >1.0 = more complex)
        """
        complexity_score = 1.0
        
        # Large offering amounts may require more analysis
        offering_amount = filing_entry.get('offering_amount', 0)
        if offering_amount > 100000000:  # $100M+
            complexity_score += 0.3
        elif offering_amount > 10000000:  # $10M+
            complexity_score += 0.1
        
        # Long descriptions require more processing
        description = filing_entry.get('description', '')
        if len(description) > 1000:
            complexity_score += 0.2
        elif len(description) > 500:
            complexity_score += 0.1
        
        # Certain industries may be more complex
        industry = filing_entry.get('industry_group', '').lower()
        complex_industries = ['biotechnology', 'pharmaceutical', 'technology', 'software']
        if any(term in industry for term in complex_industries):
            complexity_score += 0.1
        
        # Multiple exemption types add complexity
        exemptions = filing_entry.get('exemption_type', '')
        if isinstance(exemptions, str) and ',' in exemptions:
            complexity_score += 0.1
        
        return min(complexity_score, 2.0)  # Cap at 2.0
    
    def calculate_optimal_batch_size(self, filings: List[Dict[str, Any]]) -> int:
        """
        Calculate optimal batch size based on current system state and filing complexity.
        
        Args:
            filings: List of filings to process
            
        Returns:
            Optimal batch size
        """
        if not self.memory_monitor:
            return self.current_batch_size
        
        # Get current memory status
        memory_status = self.memory_monitor.check_memory_status()
        available_memory_gb = memory_status.get('available_memory_gb', 10.0)
        
        # Calculate average complexity of filings
        if filings:
            avg_complexity = sum(self.assess_filing_complexity(filing) for filing in filings) / len(filings)
        else:
            avg_complexity = 1.0
        
        # Adjust memory requirement based on complexity
        adjusted_memory_per_filing = self.memory_per_filing_gb * avg_complexity
        
        # Calculate theoretical max batch size based on memory
        memory_based_batch_size = max(1, int(available_memory_gb / adjusted_memory_per_filing))
        
        # Apply constraints
        optimal_batch_size = min(
            memory_based_batch_size,
            self.max_batch_size,
            len(filings) if filings else self.max_batch_size
        )
        optimal_batch_size = max(optimal_batch_size, self.min_batch_size)
        
        # Log adjustment reasoning
        if optimal_batch_size != self.current_batch_size:
            logger.info(f"Adjusting batch size: {self.current_batch_size} → {optimal_batch_size} "
                       f"(available_memory: {available_memory_gb:.1f}GB, "
                       f"avg_complexity: {avg_complexity:.2f})")
            
            if memory_status.get('is_warning', False):
                self.processing_stats["memory_pressure_adjustments"] += 1
            
            if avg_complexity > 1.2:
                self.processing_stats["complexity_adjustments"] += 1
        
        return optimal_batch_size
    
    def create_batches(self, filings: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """
        Create optimally-sized batches from a list of filings.
        
        Args:
            filings: List of filings to batch
            
        Returns:
            List of batches
        """
        if not filings:
            return []
        
        # Calculate optimal batch size
        optimal_batch_size = self.calculate_optimal_batch_size(filings)
        self.current_batch_size = optimal_batch_size
        
        # Create batches
        batches = []
        for i in range(0, len(filings), optimal_batch_size):
            batch = filings[i:i + optimal_batch_size]
            batches.append(batch)
        
        logger.info(f"Created {len(batches)} batches with size {optimal_batch_size} "
                   f"for {len(filings)} filings")
        
        return batches
    
    def process_batch_concurrent(self, 
                               batch: List[Dict[str, Any]], 
                               processing_function: Callable,
                               **kwargs) -> List[Dict[str, Any]]:
        """
        Process a batch of filings concurrently.
        
        Args:
            batch: Batch of filings to process
            processing_function: Function to process each filing
            **kwargs: Additional arguments for processing function
            
        Returns:
            List of processed results
        """
        if not batch:
            return []
        
        start_time = time.time()
        results = []
        
        # Determine number of workers based on batch size
        num_workers = min(self.max_workers, len(batch))
        
        logger.info(f"Processing batch of {len(batch)} filings with {num_workers} workers")
        
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            # Submit all tasks
            future_to_filing = {
                executor.submit(processing_function, filing, **kwargs): filing 
                for filing in batch
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_filing):
                filing = future_to_filing[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    # Monitor memory during processing
                    if self.memory_monitor:
                        self.memory_monitor.handle_memory_pressure()
                        
                except Exception as e:
                    logger.error(f"Error processing filing {filing.get('issuer_name', 'unknown')}: {e}")
                    # Add error result to maintain batch integrity
                    error_result = filing.copy()
                    error_result['processing_error'] = str(e)
                    results.append(error_result)
        
        # Update statistics
        batch_time = time.time() - start_time
        self._update_processing_stats(len(batch), batch_time)
        
        logger.info(f"Completed batch processing in {batch_time:.2f}s "
                   f"({batch_time/len(batch):.2f}s per filing)")
        
        return results
    
    def process_filings_adaptive(self, 
                               filings: List[Dict[str, Any]], 
                               processing_function: Callable,
                               **kwargs) -> List[Dict[str, Any]]:
        """
        Process filings using adaptive batching.
        
        Args:
            filings: List of filings to process
            processing_function: Function to process each filing
            **kwargs: Additional arguments for processing function
            
        Returns:
            List of all processed results
        """
        if not filings:
            return []
        
        logger.info(f"Starting adaptive processing of {len(filings)} filings")
        
        # Create adaptive batches
        batches = self.create_batches(filings)
        
        all_results = []
        
        for i, batch in enumerate(batches, 1):
            logger.info(f"Processing batch {i}/{len(batches)}")
            
            # Process batch concurrently
            batch_results = self.process_batch_concurrent(
                batch, processing_function, **kwargs
            )
            
            all_results.extend(batch_results)
            
            # Check memory status between batches
            if self.memory_monitor:
                memory_status = self.memory_monitor.check_memory_status()
                if memory_status.get('is_critical', False):
                    logger.warning("Critical memory usage detected, forcing cleanup")
                    self.memory_monitor.handle_memory_pressure(force=True)
                    
                    # Reduce batch size for remaining batches
                    if i < len(batches):
                        self.current_batch_size = max(
                            self.min_batch_size, 
                            self.current_batch_size // 2
                        )
                        logger.info(f"Reduced batch size to {self.current_batch_size} due to memory pressure")
        
        logger.info(f"Completed adaptive processing of {len(filings)} filings, "
                   f"produced {len(all_results)} results")
        
        return all_results
    
    def _update_processing_stats(self, batch_size: int, batch_time: float):
        """Update processing statistics."""
        with self._lock:
            self.processing_stats["batches_processed"] += 1
            self.processing_stats["total_filings"] += batch_size
            self.processing_stats["total_processing_time"] += batch_time
            
            # Calculate averages
            self.processing_stats["avg_batch_time"] = (
                self.processing_stats["total_processing_time"] / 
                self.processing_stats["batches_processed"]
            )
            
            self.processing_stats["avg_filing_time"] = (
                self.processing_stats["total_processing_time"] / 
                self.processing_stats["total_filings"]
            )
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        with self._lock:
            stats = self.processing_stats.copy()
            stats["current_batch_size"] = self.current_batch_size
            stats["throughput_filings_per_hour"] = (
                3600 / max(stats["avg_filing_time"], 0.001)
            )
            return stats
    
    def reset_stats(self):
        """Reset processing statistics."""
        with self._lock:
            self.processing_stats = {
                "batches_processed": 0,
                "total_filings": 0,
                "total_processing_time": 0.0,
                "avg_batch_time": 0.0,
                "avg_filing_time": 0.0,
                "memory_pressure_adjustments": 0,
                "complexity_adjustments": 0
            }
            logger.info("Processing statistics reset")
