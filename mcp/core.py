#!/usr/bin/env python3
"""
Model Control Point (MCP) - Core Orchestration Module

The MCP acts as the central orchestrator between:
1. Data ingestion (SEC Form D filings from ATOM feed and bulk ZIPs)
2. LLM processing (using Mixtral via MLX)
3. Tool execution (news scraping, database operations, email alerts)
4. Output actions (scoring, visualization, email alerts)

It handles:
- Parsing and formatting incoming data
- Enriching with historical comparisons and news context
- Managing LLM prompt creation
- Processing LLM output to extract scores and summaries
- Executing tools based on LLM decisions
- Triggering output actions based on relevance thresholds

Phase 3 Enhancements:
- Multi-stage analysis pipeline (screening → detailed analysis → action)
- Self-improving prompts with evaluation framework
- Chain-of-thought reasoning capabilities
- Specialized industry and offering-type analysis
"""

import os
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

# Local imports
from mcp.data_processor import DataProcessor
from mcp.prompt_manager import PromptManager
from mcp.tool_orchestrator import ToolOrchestrator
from mcp.tool_aware_prompts import ToolAwarePromptManager
from visualization.heatmap import generate_heatmap
from models.mixtral_mlx import MixtralMLX
from models.persistent_model_manager import PersistentModelManager
from mcp.tools.registry import registry
from mcp.tools.init_registry import init_registry
from utils.memory_monitor import MemoryMonitor, memory_monitor

# Phase 3 imports
from mcp.analysis_stages import ScreeningStage, DetailedAnalysisStage, ActionGenerationStage
from mcp.prompt_evaluation import PromptEvaluator
from mcp.specialized_analysis import IndustryAnalyzer, OfferingAnalyzer, ComparativeAnalyzer

# Optimization imports
from mcp.optimized_prompt_manager import OptimizedPromptManager
from mcp.adaptive_batch_processor import AdaptiveBatchProcessor

class ModelControlPoint:
    """
    Model Control Point (MCP) for orchestrating the SEC Form D analysis pipeline.
    """

    def __init__(self,
                 data_dir: str = "data",
                 model_path: Optional[str] = None,
                 models_dir: str = "models",
                 relevance_threshold: float = 0.7,
                 email_threshold: float = 0.8,
                 screening_threshold: float = 0.3,
                 generation_timeout: int = 60,
                 temperature: float = 0.1,
                 max_tokens: int = 1024,
                 init_tools: bool = True,
                 use_multi_stage: bool = True,
                 use_prompt_evaluation: bool = True,
                 use_specialized_analysis: bool = True,
                 memory_monitor=None,
                 batch_size: int = 5):
        """
        Initialize the MCP with configuration parameters.

        Args:
            data_dir: Directory for data storage
            model_path: Path to the MLX model weights (if None, uses default)
            models_dir: Directory to store downloaded models
            relevance_threshold: Threshold for considering a filing relevant
            email_threshold: Threshold for triggering email alerts
            screening_threshold: Threshold for proceeding from screening to detailed analysis
            generation_timeout: Timeout in seconds for model generation
            temperature: Sampling temperature (lower = more deterministic)
            max_tokens: Maximum tokens to generate
            init_tools: Whether to initialize the tool registry
            use_multi_stage: Whether to use multi-stage analysis pipeline
            use_prompt_evaluation: Whether to use prompt evaluation and improvement
            use_specialized_analysis: Whether to use specialized industry/offering analysis
            memory_monitor: Enhanced memory monitor instance for memory management
            batch_size: Batch size for concurrent filing processing
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True, parents=True)

        # Create models directory if it doesn't exist
        models_dir_path = Path(models_dir)
        models_dir_path.mkdir(exist_ok=True, parents=True)

        # Create evaluation directory if needed
        eval_dir = self.data_dir / "prompt_evaluation"
        eval_dir.mkdir(exist_ok=True, parents=True)

        # Store configuration
        self.relevance_threshold = relevance_threshold
        self.email_threshold = email_threshold
        self.screening_threshold = screening_threshold
        self.use_multi_stage = use_multi_stage
        self.use_prompt_evaluation = use_prompt_evaluation
        self.use_specialized_analysis = use_specialized_analysis
        self.memory_monitor = memory_monitor
        self.batch_size = batch_size

        # Initialize base components
        self.data_processor = DataProcessor(data_dir=data_dir)
        self.prompt_manager = PromptManager()

        # Initialize optimization components
        self.optimized_prompt_manager = OptimizedPromptManager(
            max_tokens=8000,  # Conservative limit for Mixtral
            compression_ratio=0.5
        )

        self.adaptive_batch_processor = AdaptiveBatchProcessor(
            memory_monitor=memory_monitor,
            initial_batch_size=batch_size,
            memory_per_filing_gb=0.5,
            max_workers=min(4, batch_size)
        )

        logging.info("Initialized optimization components: OptimizedPromptManager and AdaptiveBatchProcessor")

        # Initialize tool orchestrator (will be set up after model initialization)
        self.tool_orchestrator = None
        self.tool_aware_prompts = None

        # Initialize Phase 3 components (will be updated with tool orchestrator later)
        if self.use_multi_stage:
            self.screening_stage = ScreeningStage(screening_threshold=screening_threshold)
            self.detailed_analysis_stage = DetailedAnalysisStage(relevance_threshold=relevance_threshold)
            self.action_generation_stage = ActionGenerationStage(email_threshold=email_threshold)
            logging.info("Initialized multi-stage analysis pipeline")

        if self.use_prompt_evaluation:
            self.prompt_evaluator = PromptEvaluator(data_dir=str(eval_dir))
            logging.info("Initialized prompt evaluation framework")

        if self.use_specialized_analysis:
            self.industry_analyzer = IndustryAnalyzer()
            self.offering_analyzer = OfferingAnalyzer()
            self.comparative_analyzer = ComparativeAnalyzer()
            logging.info("Initialized specialized analysis modules")

        # Initialize tool registry
        if init_tools:
            try:
                init_registry()
                self.registry = registry
                logging.info(f"Successfully initialized tool registry with {len(self.registry.list_tools())} tools")
            except Exception as e:
                logging.warning(f"Failed to initialize tool registry: {e}")
                self.registry = None
        else:
            self.registry = None

        # Initialize model with persistent model manager for better performance
        self.persistent_model = None
        self.model = None

        try:
            # Try persistent model manager first for better performance
            self.persistent_model = PersistentModelManager(
                model_path=model_path,
                temperature=temperature,
                max_tokens=min(768, max_tokens)
            )
            self.model_available = True
            logging.info("Successfully initialized persistent model manager")
        except Exception as e:
            logging.warning(f"Failed to initialize persistent model manager: {e}")

            # Fallback to original model
            try:
                self.model = MixtralMLX(
                    model_path=model_path,
                    models_dir=models_dir,
                    generation_timeout=generation_timeout,
                    temperature=temperature,
                    max_tokens=min(768, max_tokens),
                    use_multiprocessing=True
                )
                self.model_available = True
                logging.info(f"Successfully initialized fallback model: {self.model.model_name if hasattr(self.model, 'model_name') else 'unknown'}")
            except Exception as e2:
                logging.warning(f"Failed to initialize fallback model: {e2}")
                self.model_available = False

        # Initialize tool orchestrator and tool-aware prompts after model is ready
        if self.registry and self.model_available:
            try:
                self.tool_orchestrator = ToolOrchestrator(
                    registry=self.registry,
                    model=self.model
                )
                self.tool_aware_prompts = ToolAwarePromptManager(
                    tool_orchestrator=self.tool_orchestrator
                )

                # Update analysis stages with tool orchestrator
                if self.use_multi_stage:
                    self.screening_stage.tool_orchestrator = self.tool_orchestrator
                    self.detailed_analysis_stage.tool_orchestrator = self.tool_orchestrator
                    self.action_generation_stage.tool_orchestrator = self.tool_orchestrator
                    logging.info("Updated analysis stages with tool orchestrator")

                logging.info("Successfully initialized tool orchestrator and tool-aware prompts")
            except Exception as e:
                logging.warning(f"Failed to initialize tool orchestrator: {e}")
                self.tool_orchestrator = None
                self.tool_aware_prompts = None

    def process_new_filings(self,
                           feed_entries: List[Dict[str, Any]],
                           historical_context: bool = True,
                           news_context: bool = True) -> List[Dict[str, Any]]:
        """
        Process new filings from the ATOM feed with historical and news context.

        Args:
            feed_entries: List of feed entries from SEC ATOM feed
            historical_context: Whether to enrich with historical context
            news_context: Whether to enrich with news context

        Returns:
            List of processed entries with scores and summaries
        """
        if not feed_entries:
            logging.warning("No feed entries to process")
            return []

        # 1. Preprocess and normalize feed entries
        normalized_entries = self.data_processor.normalize_feed_entries(feed_entries)

        # 2. Enrich with historical context if requested
        if historical_context:
            enriched_entries = self.data_processor.enrich_with_historical_context(normalized_entries)
        else:
            enriched_entries = normalized_entries

        # 3. Enrich with news context if requested and registry is available
        if news_context and self.registry:
            try:
                for entry in enriched_entries:
                    # Extract company name and industry for news search
                    company_name = entry.get("issuer_name", "")
                    industry = entry.get("industry_group", "")

                    if company_name:
                        # Create search query
                        query = f"{company_name} {industry} funding investment"

                        # Execute news scraper tool
                        news_results = self.registry.execute_tool(
                            "news_scraper",
                            query=query,
                            max_results=3,
                            days_back=30
                        )

                        # Add news context to entry
                        if news_results and news_results.get("results"):
                            entry["news_context"] = news_results["results"]
                            logging.info(f"Added {len(news_results['results'])} news items for {company_name}")
                        else:
                            entry["news_context"] = []
                            logging.info(f"No news found for {company_name}")
            except Exception as e:
                logging.error(f"Error enriching with news context: {e}")
                # Continue without news context

        # 4. Add specialized analysis if enabled
        if self.use_specialized_analysis:
            try:
                for entry in enriched_entries:
                    # Add industry-specific analysis template
                    industry_template = self.industry_analyzer.get_industry_template(entry)
                    entry["industry_analysis_template"] = industry_template

                    # Add offering-specific analysis template
                    offering_template = self.offering_analyzer.get_offering_template(entry)
                    entry["offering_analysis_template"] = offering_template

                    # Add comparative analysis if similar filings available
                    similar_filings = entry.get("similar_filings", [])
                    if similar_filings:
                        comparative_analysis = self.comparative_analyzer.analyze_comparatively(
                            entry, similar_filings
                        )
                        entry["comparative_analysis"] = comparative_analysis

                logging.info("Added specialized analysis templates to entries")
            except Exception as e:
                logging.error(f"Error adding specialized analysis: {e}")
                # Continue without specialized analysis

        # 5. Process entries - either with multi-stage pipeline or legacy approach
        processed_entries = []

        if self.use_multi_stage and self.model_available:
            # Use multi-stage analysis pipeline
            processed_entries = self._process_with_multi_stage_pipeline(enriched_entries)
        else:
            # Use legacy single-prompt approach
            processed_entries = self._process_with_legacy_approach(enriched_entries)

        # 6. Sort by relevance score
        processed_entries.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)

        return processed_entries

    def _process_with_multi_stage_pipeline(self, enriched_entries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process entries using the multi-stage analysis pipeline with adaptive batching.

        Args:
            enriched_entries: Enriched filing entries

        Returns:
            Processed entries with analysis results
        """
        if not enriched_entries:
            return []

        logging.info(f"Starting multi-stage pipeline processing for {len(enriched_entries)} entries")

        # Use adaptive batch processor for concurrent processing
        processed_entries = self.adaptive_batch_processor.process_filings_adaptive(
            filings=enriched_entries,
            processing_function=self._process_single_filing_multi_stage
        )

        # Log processing statistics
        stats = self.adaptive_batch_processor.get_processing_stats()
        logging.info(f"Multi-stage processing completed: {stats['total_filings']} filings, "
                    f"avg time: {stats['avg_filing_time']:.2f}s per filing, "
                    f"throughput: {stats['throughput_filings_per_hour']:.1f} filings/hour")

        return processed_entries

    def _process_single_filing_multi_stage(self, entry: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single filing through the multi-stage pipeline.

        Args:
            entry: Single filing entry to process

        Returns:
            Processed entry with analysis results
        """
        entry_id = entry.get('id', 'unknown')
        logging.debug(f"Processing entry: {entry_id}")

        try:
                # Stage 1: Screening
            screening_prompt = self.screening_stage.create_prompt(entry)

            # Apply prompt evaluation if enabled
            if self.use_prompt_evaluation:
                # Generate variations and get best prompt
                variations = self.prompt_evaluator.generate_variations(screening_prompt)
                screening_prompt = self.prompt_evaluator.get_best_prompt(variations)

            # Process with LLM
            screening_output = self._process_with_llm(
                entry_id,
                screening_prompt,
                "screening",
                filing_data=entry
            )

            # Parse output
            screening_results = self.screening_stage.parse_output(screening_output)

            # Evaluate prompt if enabled
            if self.use_prompt_evaluation:
                metrics = self.prompt_evaluator.evaluate_prompt(
                    screening_prompt,
                    screening_output,
                    screening_results
                )
                # Refine prompt if needed
                if metrics["overall_score"] < 0.7:
                    refined_prompt = self.prompt_evaluator.refine_prompt(
                        screening_prompt,
                        metrics
                    )
                    # Store for future use
                    logging.debug(f"Refined screening prompt (score: {metrics['overall_score']:.2f})")

            # Store screening results
            entry["screening_results"] = screening_results

            # Check if should proceed to detailed analysis
            if self.screening_stage.should_proceed(screening_results):
                logging.debug(f"Entry {entry_id} passed screening with score: {screening_results.get('initial_score', 0):.2f}")

                # Stage 2: Detailed Analysis
                detailed_prompt = self.detailed_analysis_stage.create_prompt(
                    entry,
                    context=screening_results
                )

                # Apply prompt evaluation if enabled
                if self.use_prompt_evaluation:
                    variations = self.prompt_evaluator.generate_variations(detailed_prompt)
                    detailed_prompt = self.prompt_evaluator.get_best_prompt(variations)

                # Process with LLM
                detailed_output = self._process_with_llm(
                    entry_id,
                    detailed_prompt,
                    "detailed_analysis",
                    filing_data=entry
                )

                # Parse output
                detailed_results = self.detailed_analysis_stage.parse_output(detailed_output)

                # Evaluate prompt if enabled
                if self.use_prompt_evaluation:
                    metrics = self.prompt_evaluator.evaluate_prompt(
                        detailed_prompt,
                        detailed_output,
                        detailed_results
                    )

                # Store detailed results
                entry["detailed_results"] = detailed_results

                # Update entry with key fields for compatibility
                entry.update({
                    "relevance_score": detailed_results.get("relevance_score", 0.0),
                    "summary": detailed_results.get("summary", ""),
                    "analysis_timestamp": datetime.now().isoformat()
                })

                # Check if should proceed to action generation
                if self.detailed_analysis_stage.should_proceed(detailed_results):
                    logging.debug(f"Entry {entry_id} passed detailed analysis with score: {detailed_results.get('relevance_score', 0):.2f}")

                    # Stage 3: Action Generation
                    action_prompt = self.action_generation_stage.create_prompt(
                        entry,
                        context=detailed_results
                    )

                    # Apply prompt evaluation if enabled
                    if self.use_prompt_evaluation:
                        variations = self.prompt_evaluator.generate_variations(action_prompt)
                        action_prompt = self.prompt_evaluator.get_best_prompt(variations)

                    # Process with LLM
                    action_output = self._process_with_llm(
                        entry_id,
                        action_prompt,
                        "action_generation",
                        filing_data=entry
                    )

                    # Parse output
                    action_results = self.action_generation_stage.parse_output(action_output)

                    # Evaluate prompt if enabled
                    if self.use_prompt_evaluation:
                        metrics = self.prompt_evaluator.evaluate_prompt(
                            action_prompt,
                            action_output,
                            action_results
                        )

                    # Store action results
                    entry["action_results"] = action_results

                    # Update entry with email draft for compatibility
                    entry["email_draft"] = action_results.get("email_draft", "")
                else:
                    logging.debug(f"Entry {entry_id} did not meet threshold for action generation")
                    entry["email_draft"] = ""  # No email for low relevance
            else:
                logging.debug(f"Entry {entry_id} did not pass screening")
                # Set default values for entries that don't pass screening
                entry.update({
                    "relevance_score": 0.0,
                    "summary": f"Did not pass screening (score: {screening_results.get('initial_score', 0):.2f})",
                    "email_draft": "",
                    "analysis_timestamp": datetime.now().isoformat()
                })

        except Exception as e:
            logging.error(f"Error in multi-stage pipeline for entry {entry_id}: {e}")
            # Set error values
            entry.update({
                "relevance_score": 0.0,
                "summary": f"Error in multi-stage pipeline: {str(e)}",
                "email_draft": "",
                "analysis_timestamp": datetime.now().isoformat(),
                "error": str(e)
            })

        return entry

    def _process_with_llm(self, entry_id: str, prompt: str, stage_name: str,
                         filing_data: Dict[str, Any] = None) -> str:
        """
        Process a prompt with the LLM using persistent model manager for caching.

        Args:
            entry_id: ID of the entry being processed
            prompt: Prompt to send to LLM
            stage_name: Name of the processing stage
            filing_data: Filing data for caching (optional)

        Returns:
            LLM output text
        """
        if not self.model_available:
            raise ValueError("Model not available")

        try:
            # Log the start of generation
            logging.info(f"Starting {stage_name} generation for entry: {entry_id}")

            # Use persistent model manager if available
            if self.persistent_model:
                # Use cached analysis if filing data is provided
                if filing_data:
                    result = self.persistent_model.analyze_filing(
                        filing_data=filing_data,
                        prompt=prompt,
                        filing_id=filing_data.get('filing_id')
                    )

                    if result.get('cached'):
                        logging.info(f"Using cached result for entry: {entry_id}")

                    # Return the summary as the LLM output for compatibility
                    import json
                    return json.dumps({
                        'relevance_score': result.get('relevance_score', 0.0),
                        'summary': result.get('summary', ''),
                        'email_draft': result.get('email_draft', '')
                    })
                else:
                    # Direct analysis without caching
                    result = self.persistent_model.analyze_filing(
                        filing_data={'id': entry_id, 'title': '', 'summary': ''},
                        prompt=prompt
                    )
                    import json
                    return json.dumps({
                        'relevance_score': result.get('relevance_score', 0.0),
                        'summary': result.get('summary', ''),
                        'email_draft': result.get('email_draft', '')
                    })
            else:
                # Fallback to original model
                def progress_callback(token_text):
                    if len(token_text) > 5:
                        logging.debug(f"Generation in progress ({stage_name}): received {len(token_text)} chars")

                llm_output = self.model.generate(prompt, callback=progress_callback)
                self.model._cleanup_resources()
                return llm_output

        except Exception as e:
            logging.error(f"Error in {stage_name} generation for entry {entry_id}: {e}")
            # Clean up resources even on error
            if self.model:
                try:
                    self.model._cleanup_resources()
                except Exception as cleanup_error:
                    logging.error(f"Error during cleanup: {cleanup_error}")
            raise

    def _process_with_legacy_approach(self, enriched_entries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process entries using the legacy single-prompt approach.

        Args:
            enriched_entries: Enriched filing entries

        Returns:
            Processed entries with analysis results
        """
        # Initialize memory monitor
        monitor = MemoryMonitor()
        monitor.set_baseline()

        processed_entries = []

        # Generate prompts for LLM analysis
        prompts = []
        for entry in enriched_entries:
            # Update prompt creation to include news context
            prompt = self.prompt_manager.create_analysis_prompt(entry)
            prompts.append((entry, prompt))

        monitor.checkpoint("prompts_generated")

        # Process with LLM
        entry_count = 0
        for entry, prompt in prompts:
            entry_count += 1
            entry_id = entry.get('id', 'unknown')

            # Memory checkpoint before processing each entry
            monitor.checkpoint(f"entry_{entry_count}_start")

            # Check memory limit before processing
            if not monitor.check_memory_limit(limit_gb=6.0):  # 6GB limit
                logging.error(f"Memory limit exceeded before processing entry {entry_id}")
                monitor.force_cleanup()
                # Continue with mock processing to avoid crash
                entry.update({
                    "relevance_score": 0.0,
                    "summary": "Skipped due to memory limit",
                    "email_draft": "",
                    "analysis_timestamp": datetime.now().isoformat(),
                    "error": "Memory limit exceeded"
                })
                processed_entries.append(entry)
                continue

            if self.model_available:
                try:
                    # Log the start of generation
                    logging.info(f"Starting generation for entry: {entry_id}")

                    # Use persistent model manager if available for caching
                    if self.persistent_model:
                        result = self.persistent_model.analyze_filing(
                            filing_data=entry,
                            prompt=prompt,
                            filing_id=entry.get('filing_id')
                        )

                        if result.get('cached'):
                            logging.info(f"Using cached result for entry: {entry_id}")

                        # Update entry with cached or new analysis
                        entry.update({
                            "relevance_score": result.get("relevance_score", 0.0),
                            "summary": result.get("summary", ""),
                            "email_draft": result.get("email_draft", ""),
                            "analysis_timestamp": result.get("analysis_timestamp", datetime.now().isoformat()),
                            "cached": result.get("cached", False)
                        })
                    else:
                        # Fallback to original model
                        def progress_callback(token_text):
                            if len(token_text) > 5:
                                logging.debug(f"Generation in progress: received {len(token_text)} chars")

                        llm_output = self.model.generate(prompt, callback=progress_callback)
                        parsed_output = self.prompt_manager.parse_llm_output(llm_output)

                        entry.update({
                            "relevance_score": parsed_output.get("relevance_score", 0.0),
                            "summary": parsed_output.get("summary", ""),
                            "email_draft": parsed_output.get("email_draft", ""),
                            "analysis_timestamp": datetime.now().isoformat()
                        })

                        # Clean up resources after processing each entry
                        self.model._cleanup_resources()

                    # Log successful generation
                    logging.info(f"Generation completed successfully for entry: {entry_id}")

                    # Force garbage collection every 3 entries (only for non-persistent model)
                    if not self.persistent_model and entry_count % 3 == 0:
                        monitor.force_cleanup()

                    # Memory checkpoint after processing
                    monitor.checkpoint(f"entry_{entry_count}_complete")

                except KeyboardInterrupt:
                    # Handle user interruption gracefully
                    logging.warning("User interrupted generation. Cleaning up...")
                    self.model._cleanup_resources()
                    entry.update({
                        "relevance_score": 0.0,
                        "summary": "Generation interrupted by user",
                        "email_draft": "",
                        "analysis_timestamp": datetime.now().isoformat(),
                        "error": "User interrupted"
                    })
                    # Re-raise to allow proper program termination
                    raise

                except Exception as e:
                    logging.error(f"Error processing entry with LLM: {e}")
                    entry.update({
                        "relevance_score": 0.0,
                        "summary": "Error processing with LLM",
                        "email_draft": "",
                        "analysis_timestamp": datetime.now().isoformat(),
                        "error": str(e)
                    })

                    # Clean up resources even on error
                    try:
                        self.model._cleanup_resources()
                    except Exception as cleanup_error:
                        logging.error(f"Error during cleanup: {cleanup_error}")
            else:
                # Mock scoring if model not available
                entry.update({
                    "relevance_score": 0.5,  # Default mid-range score
                    "summary": "Model not available - mock summary",
                    "email_draft": "Model not available - mock email content",
                    "analysis_timestamp": datetime.now().isoformat(),
                    "mock_processing": True
                })

            processed_entries.append(entry)

        return processed_entries

    def generate_visualizations(self, processed_entries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate visualizations for the processed entries.

        Args:
            processed_entries: List of processed entries with scores

        Returns:
            Dictionary with visualization paths and metadata
        """
        # Filter entries above relevance threshold
        relevant_entries = [e for e in processed_entries
                           if e.get("relevance_score", 0) >= self.relevance_threshold]

        if not relevant_entries:
            logging.info("No entries above relevance threshold for visualization")
            return {"heatmap_path": None, "relevant_count": 0}

        # Generate heatmap
        try:
            heatmap_path = generate_heatmap(relevant_entries,
                                           output_dir=self.data_dir / "visualizations")
            return {
                "heatmap_path": heatmap_path,
                "relevant_count": len(relevant_entries)
            }
        except Exception as e:
            logging.error(f"Error generating heatmap: {e}")
            return {"heatmap_path": None, "relevant_count": len(relevant_entries), "error": str(e)}

    def should_send_email(self, processed_entries: List[Dict[str, Any]]) -> bool:
        """
        Determine if an email alert should be sent based on thresholds.

        Args:
            processed_entries: List of processed entries with scores

        Returns:
            Boolean indicating whether to send an email
        """
        # Check if any entry exceeds the email threshold
        for entry in processed_entries:
            if entry.get("relevance_score", 0) >= self.email_threshold:
                return True

        return False

    def get_email_content(self,
                         processed_entries: List[Dict[str, Any]],
                         visualization_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate email content for alert.

        Args:
            processed_entries: List of processed entries with scores
            visualization_data: Visualization metadata from generate_visualizations

        Returns:
            Dictionary with email subject, body, and attachments
        """
        # Filter entries above email threshold
        high_relevance_entries = [e for e in processed_entries
                                 if e.get("relevance_score", 0) >= self.email_threshold]

        if not high_relevance_entries:
            return {
                "subject": "SEC Form D Filing Alert - No High Relevance Filings",
                "body_html": "<p>No filings above the email alert threshold were found.</p>",
                "attachments": []
            }

        # Generate email content
        try:
            # Use the first high relevance entry's email draft as a starting point
            # Prefer action_results email draft if available (from multi-stage pipeline)
            if "action_results" in high_relevance_entries[0] and high_relevance_entries[0]["action_results"].get("email_draft"):
                email_draft = high_relevance_entries[0]["action_results"].get("email_draft", "")
            else:
                email_draft = high_relevance_entries[0].get("email_draft", "")

            # Build HTML content
            html_content = f"""
            <html>
            <body>
                <h1>SEC Form D Filing Alert</h1>
                <p>{email_draft}</p>
                <h2>Top Relevant Filings</h2>
                <ul>
            """

            # Add top filings with enhanced information
            for entry in high_relevance_entries[:5]:  # Top 5 max
                # Get summary - prefer detailed analysis if available
                if "detailed_results" in entry and entry["detailed_results"].get("summary"):
                    summary = entry["detailed_results"].get("summary", "")
                else:
                    summary = entry.get("summary", "")

                # Get actionable insights if available
                actionable_insights = []
                if "action_results" in entry and entry["action_results"].get("actionable_insights"):
                    actionable_insights = entry["action_results"].get("actionable_insights", [])

                html_content += f"""
                <li>
                    <strong>{entry.get("title", "Untitled")}</strong> -
                    Score: {entry.get("relevance_score", 0):.2f}<br>
                    <p>{summary}</p>
                """

                # Add actionable insights if available
                if actionable_insights:
                    html_content += "<p><strong>Key Insights:</strong></p><ul>"
                    for insight in actionable_insights[:3]:  # Top 3 insights
                        html_content += f"<li>{insight}</li>"
                    html_content += "</ul>"

                html_content += "</li>"

            html_content += "</ul>"

            # Add reasoning chains if available (from multi-stage pipeline)
            reasoning_chains = []
            for entry in high_relevance_entries[:2]:  # Top 2 entries only to avoid email length
                if "detailed_results" in entry and entry["detailed_results"].get("reasoning_chain"):
                    reasoning_chains.append({
                        "title": entry.get("title", "Untitled"),
                        "chain": entry["detailed_results"].get("reasoning_chain", [])
                    })

            if reasoning_chains:
                html_content += f"""
                <h2>Analysis Reasoning</h2>
                <p>Step-by-step reasoning for top filings:</p>
                """

                for rc in reasoning_chains:
                    html_content += f"<h3>{rc['title']}</h3><ol>"
                    for step in rc["chain"]:
                        html_content += f"<li>{step}</li>"
                    html_content += "</ol>"

            # Add specialized analysis if available
            industry_analyses = []
            for entry in high_relevance_entries[:3]:  # Top 3 entries
                if "detailed_results" in entry and entry["detailed_results"].get("analysis", {}).get("market_analysis"):
                    industry_analyses.append({
                        "title": entry.get("title", "Untitled"),
                        "industry": entry.get("industry", "Unknown"),
                        "analysis": entry["detailed_results"].get("analysis", {}).get("market_analysis", "")
                    })

            if industry_analyses:
                html_content += f"""
                <h2>Industry Analysis</h2>
                <ul>
                """

                for ia in industry_analyses:
                    html_content += f"""
                    <li>
                        <strong>{ia['title']}</strong> ({ia['industry']})<br>
                        {ia['analysis']}
                    </li>
                    """

                html_content += "</ul>"

            # Add news context if available
            news_items = []
            for entry in high_relevance_entries[:3]:  # Top 3 entries
                if entry.get("news_context"):
                    for news in entry.get("news_context", [])[:2]:  # Top 2 news per entry
                        news_items.append({
                            "title": news.get("title", ""),
                            "source": news.get("source", ""),
                            "date": news.get("date", ""),
                            "url": news.get("url", ""),
                            "snippet": news.get("snippet", ""),
                            "company": entry.get("issuer_name", "")
                        })

            if news_items:
                html_content += f"""
                <h2>Related News</h2>
                <ul>
                """

                for news in news_items:
                    html_content += f"""
                    <li>
                        <strong><a href="{news['url']}">{news['title']}</a></strong> -
                        {news['source']} ({news['date'][:10]})<br>
                        <em>Related to: {news['company']}</em><br>
                        {news['snippet']}
                    </li>
                    """

                html_content += "</ul>"

            # Add follow-up questions if available
            follow_up_questions = []
            for entry in high_relevance_entries[:3]:  # Top 3 entries
                if "action_results" in entry and entry["action_results"].get("follow_up_questions"):
                    for q in entry["action_results"].get("follow_up_questions", [])[:2]:  # Top 2 questions per entry
                        follow_up_questions.append({
                            "question": q,
                            "company": entry.get("issuer_name", "")
                        })

            if follow_up_questions:
                html_content += f"""
                <h2>Follow-up Questions</h2>
                <ul>
                """

                for q in follow_up_questions:
                    html_content += f"""
                    <li>
                        <strong>{q['company']}:</strong> {q['question']}
                    </li>
                    """

                html_content += "</ul>"

            # Add heatmap if available
            if visualization_data.get("heatmap_path"):
                html_content += f"""
                <h2>Heatmap Visualization</h2>
                <p>See attached heatmap visualization.</p>
                """
                attachments = [visualization_data["heatmap_path"]]
            else:
                attachments = []

            html_content += "</body></html>"

            return {
                "subject": f"SEC Form D Alert: {len(high_relevance_entries)} High Relevance Filings",
                "body_html": html_content,
                "attachments": attachments
            }

        except Exception as e:
            logging.error(f"Error generating email content: {e}")
            return {
                "subject": "SEC Form D Filing Alert - Error",
                "body_html": f"<p>Error generating email content: {e}</p>",
                "attachments": []
            }

    def send_email_alert(self, email_content: Dict[str, Any], recipients: List[str]) -> Dict[str, Any]:
        """
        Send email alert using Microsoft 365 tool.

        Args:
            email_content: Email content from get_email_content
            recipients: List of recipient email addresses

        Returns:
            Dictionary with email sending status
        """
        if not self.registry:
            logging.warning("Tool registry not available, cannot send email")
            return {"status": "error", "message": "Tool registry not available"}

        try:
            # Convert attachments to format expected by Microsoft 365 tool
            attachments = []
            for attachment_path in email_content.get("attachments", []):
                if isinstance(attachment_path, (str, Path)):
                    path = Path(attachment_path)
                    if path.exists():
                        with open(path, "rb") as f:
                            content = f.read()

                        attachments.append({
                            "name": path.name,
                            "content": content,
                            "content_type": None  # Let the tool determine content type
                        })

            # Send email using Microsoft 365 tool
            result = self.registry.execute_tool(
                "microsoft365_mcp_server",
                action="send_email",
                to=recipients,
                subject=email_content["subject"],
                body=email_content["body_html"],
                body_type="html",
                attachments=attachments
            )

            logging.info(f"Email sent to {len(recipients)} recipients")
            return result
        except Exception as e:
            logging.error(f"Error sending email: {e}")
            return {"status": "error", "message": str(e)}
