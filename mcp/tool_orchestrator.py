#!/usr/bin/env python3
"""
MCP Tool Orchestrator

Provides intelligent tool selection and orchestration capabilities for the LLM:
1. Tool-aware prompt generation
2. LLM-driven tool selection and execution
3. Intelligent tool chaining and workflow orchestration
4. Context-aware tool recommendations
"""

import json
import logging
import re
import asyncio
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from functools import wraps

logger = logging.getLogger(__name__)

class ToolOrchestrator:
    """
    Orchestrates tool selection and execution based on LLM decisions.
    """
    
    def __init__(self, registry, model=None):
        """
        Initialize the tool orchestrator.
        
        Args:
            registry: Tool registry instance
            model: LLM model instance for tool selection
        """
        self.registry = registry
        self.model = model

        # Performance tracking
        self.tool_selection_cache = {}
        self.execution_stats = {
            "total_selections": 0,
            "cache_hits": 0,
            "total_executions": 0,
            "failed_executions": 0,
            "avg_selection_time": 0.0,
            "avg_execution_time": 0.0
        }

        # Stage-specific tool filters
        self.stage_tool_filters = {
            "screening": ["news_scraper", "postgresql_mcp_server"],  # Fast tools for initial screening
            "detailed_analysis": ["news_scraper", "postgresql_mcp_server", "github_mcp_server"],  # More comprehensive
            "action_generation": ["microsoft365_mcp_server", "json_mcp_server", "github_mcp_server"]  # Output-focused
        }

        # Tool selection prompt template
        self.tool_selection_template = """
You are an expert financial analyst with access to specialized tools for SEC Form D analysis.

# Available Tools
{tool_descriptions}

# Filing Context
{filing_context}

# Analysis Stage
Current stage: {analysis_stage}
Previous results: {previous_results}

# Task
Based on the filing context and analysis stage, determine which tools (if any) should be used to enhance the analysis.

Consider:
1. Filing characteristics (industry, amount, company type)
2. Analysis stage requirements
3. Available data and context
4. Tool capabilities and relevance

# Response Format
Respond in JSON format:
{{
  "tools_to_use": [
    {{
      "tool_name": "string",
      "reason": "string",
      "parameters": {{
        "param1": "value1",
        "param2": "value2"
      }},
      "priority": int  // 1-5, where 5 is highest priority
    }}
  ],
  "tool_chain": [
    {{
      "step": int,
      "tool_name": "string",
      "depends_on": ["tool_name1", "tool_name2"],  // Tools that must complete first
      "condition": "string"  // Optional condition for execution
    }}
  ],
  "reasoning": "string"  // Explanation of tool selection strategy
}}
"""

    def get_stage_filtered_tools(self, analysis_stage: str) -> List[str]:
        """
        Get tools filtered for the specific analysis stage.

        Args:
            analysis_stage: Current analysis stage

        Returns:
            List of tool names appropriate for the stage
        """
        if analysis_stage in self.stage_tool_filters:
            return self.stage_tool_filters[analysis_stage]

        # Default: return all tools if stage not recognized
        if self.registry:
            return [tool['name'] for tool in self.registry.list_tools()]
        return []

    def create_compact_filing_context(self, filing_entry: Dict[str, Any]) -> str:
        """
        Create a compact filing context summary to reduce token usage.

        Args:
            filing_entry: Filing entry to summarize

        Returns:
            Compact context string
        """
        # Extract only essential information
        essentials = {
            'issuer': filing_entry.get('issuer_name', 'Unknown')[:50],  # Truncate long names
            'industry': filing_entry.get('industry_group', 'Unknown')[:30],
            'amount': filing_entry.get('offering_amount', 0),
            'date': filing_entry.get('filing_date', 'Unknown')[:10]  # Just date, no time
        }

        # Format compactly
        amount_str = f"${essentials['amount']:,.0f}" if essentials['amount'] else "Unknown"

        return f"Issuer: {essentials['issuer']} | Industry: {essentials['industry']} | Amount: {amount_str} | Date: {essentials['date']}"

    def get_cached_tool_selection(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        Get cached tool selection if available.

        Args:
            cache_key: Cache key for the selection

        Returns:
            Cached selection or None
        """
        if cache_key in self.tool_selection_cache:
            self.execution_stats["cache_hits"] += 1
            logger.debug(f"Using cached tool selection for key: {cache_key}")
            return self.tool_selection_cache[cache_key]
        return None

    def cache_tool_selection(self, cache_key: str, selection: Dict[str, Any]):
        """
        Cache a tool selection for future use.

        Args:
            cache_key: Cache key
            selection: Tool selection to cache
        """
        # Limit cache size to prevent memory bloat
        if len(self.tool_selection_cache) > 100:
            # Remove oldest entries (simple FIFO)
            oldest_key = next(iter(self.tool_selection_cache))
            del self.tool_selection_cache[oldest_key]

        self.tool_selection_cache[cache_key] = selection
        logger.debug(f"Cached tool selection for key: {cache_key}")

    def get_tool_descriptions(self, analysis_stage: str = None) -> str:
        """
        Get formatted descriptions of available tools, optionally filtered by stage.

        Args:
            analysis_stage: Optional stage to filter tools for

        Returns:
            Formatted tool descriptions for prompt inclusion
        """
        if not self.registry:
            return "No tools available."

        # Get stage-filtered tools if stage is provided
        if analysis_stage:
            allowed_tools = self.get_stage_filtered_tools(analysis_stage)
            tools = [tool for tool in self.registry.list_tools() if tool['name'] in allowed_tools]
        else:
            tools = self.registry.list_tools()

        descriptions = []

        for tool in tools:
            tool_obj = self.registry.get_tool(tool['name'])
            if tool_obj:
                schema = tool_obj.get_schema()
                
                # Extract key parameters
                key_params = []
                for param, details in schema.get('properties', {}).items():
                    if param in schema.get('required', []):
                        key_params.append(f"{param} (required)")
                    else:
                        key_params.append(f"{param} (optional)")
                
                description = f"""
**{tool['name']}** ({tool['category']})
- Purpose: {tool['description']}
- Key parameters: {', '.join(key_params[:3])}  # Show top 3 params
- Use cases: {self._get_tool_use_cases(tool['name'])}
"""
                descriptions.append(description)
        
        return '\n'.join(descriptions)
    
    def _get_tool_use_cases(self, tool_name: str) -> str:
        """Get specific use cases for a tool in SEC Form D analysis."""
        use_cases = {
            "news_scraper": "Find recent news about companies, funding rounds, industry trends",
            "github_mcp_server": "Track analysis progress, create issues for follow-up, collaborate on findings",
            "postgresql_mcp_server": "Query historical data, compare with similar filings, store analysis results",
            "microsoft365_mcp_server": "Send email alerts, schedule follow-up meetings, share analysis reports",
            "json_mcp_server": "Provide API access for external integrations, expose analysis data"
        }
        return use_cases.get(tool_name, "General purpose tool for SEC Form D analysis")
    
    def select_tools(self, 
                    filing_entry: Dict[str, Any],
                    analysis_stage: str,
                    previous_results: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Use LLM to select appropriate tools for the current context.
        
        Args:
            filing_entry: Current filing being analyzed
            analysis_stage: Current analysis stage (screening, detailed_analysis, action_generation)
            previous_results: Results from previous analysis stages
            
        Returns:
            Tool selection decisions and execution plan
        """
        if not self.model:
            logger.warning("No model available for tool selection")
            return {"tools_to_use": [], "tool_chain": [], "reasoning": "No model available"}

        # Track performance
        start_time = time.time()
        self.execution_stats["total_selections"] += 1

        # Create cache key for potential caching
        cache_key = f"{analysis_stage}_{filing_entry.get('issuer_name', 'unknown')}_{filing_entry.get('industry_group', 'unknown')}"

        # Check cache first
        cached_result = self.get_cached_tool_selection(cache_key)
        if cached_result:
            return cached_result

        # Use compact filing context to reduce token usage
        filing_context = self.create_compact_filing_context(filing_entry)

        # Compress previous results to essential information only
        prev_results_summary = self._compress_previous_results(previous_results) if previous_results else "None"

        # Create tool selection prompt with stage-filtered tools
        prompt = self.tool_selection_template.format(
            tool_descriptions=self.get_tool_descriptions(analysis_stage),
            filing_context=filing_context,
            analysis_stage=analysis_stage,
            previous_results=prev_results_summary
        )
        
        try:
            # Get LLM decision
            llm_output = self.model.generate(prompt)

            # Parse the response
            tool_decisions = self._parse_tool_selection(llm_output)

            # Cache the result for future use
            self.cache_tool_selection(cache_key, tool_decisions)

            # Update performance stats
            selection_time = time.time() - start_time
            self.execution_stats["avg_selection_time"] = (
                (self.execution_stats["avg_selection_time"] * (self.execution_stats["total_selections"] - 1) + selection_time) /
                self.execution_stats["total_selections"]
            )

            logger.info(f"LLM selected {len(tool_decisions.get('tools_to_use', []))} tools for {analysis_stage} "
                       f"(selection time: {selection_time:.2f}s)")

            return tool_decisions

        except Exception as e:
            logger.error(f"Error in tool selection: {e}")
            return {"tools_to_use": [], "tool_chain": [], "reasoning": f"Error: {str(e)}"}

    def _execute_tool_with_retry(self, tool_name: str, tool_spec: Dict[str, Any],
                                filing_entry: Dict[str, Any], results: Dict[str, Any],
                                max_retries: int = 3) -> Dict[str, Any]:
        """
        Execute a tool with exponential backoff retry logic.

        Args:
            tool_name: Name of the tool to execute
            tool_spec: Tool specification from LLM decision
            filing_entry: Filing entry being processed
            results: Results from previously executed tools
            max_retries: Maximum number of retry attempts

        Returns:
            Tool execution result
        """
        for attempt in range(max_retries + 1):
            try:
                # Enhance parameters with context
                enhanced_params = self._enhance_tool_parameters(
                    tool_spec.get('parameters', {}),
                    filing_entry,
                    results
                )

                # Execute tool
                start_time = time.time()
                tool_result = self.registry.execute_tool(tool_name, **enhanced_params)
                execution_time = time.time() - start_time

                # Update execution stats
                self.execution_stats["total_executions"] += 1
                self.execution_stats["avg_execution_time"] = (
                    (self.execution_stats["avg_execution_time"] * (self.execution_stats["total_executions"] - 1) + execution_time) /
                    self.execution_stats["total_executions"]
                )

                return {
                    "result": tool_result,
                    "reason": tool_spec.get('reason', ''),
                    "execution_time": datetime.now().isoformat(),
                    "duration_seconds": execution_time,
                    "attempt": attempt + 1
                }

            except Exception as e:
                if attempt < max_retries:
                    # Exponential backoff: wait 2^attempt seconds
                    wait_time = 2 ** attempt
                    logger.warning(f"Tool {tool_name} failed on attempt {attempt + 1}, retrying in {wait_time}s: {e}")
                    time.sleep(wait_time)
                else:
                    # Final attempt failed
                    logger.error(f"Tool {tool_name} failed after {max_retries + 1} attempts: {e}")
                    self.execution_stats["failed_executions"] += 1
                    return {
                        "error": str(e),
                        "reason": tool_spec.get('reason', ''),
                        "execution_time": datetime.now().isoformat(),
                        "attempts": max_retries + 1
                    }

    def get_execution_stats(self) -> Dict[str, Any]:
        """
        Get execution statistics for performance monitoring.

        Returns:
            Dictionary with execution statistics
        """
        stats = self.execution_stats.copy()
        stats["cache_hit_rate"] = (
            stats["cache_hits"] / max(stats["total_selections"], 1) * 100
        )
        stats["success_rate"] = (
            (stats["total_executions"] - stats["failed_executions"]) /
            max(stats["total_executions"], 1) * 100
        )
        return stats

    def execute_tool_chain(self,
                          tool_decisions: Dict[str, Any],
                          filing_entry: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the selected tools in the determined order.
        
        Args:
            tool_decisions: Tool selection decisions from select_tools()
            filing_entry: Filing entry being processed
            
        Returns:
            Combined results from all tool executions
        """
        if not self.registry:
            logger.warning("No registry available for tool execution")
            return {}
        
        results = {}
        executed_tools = set()
        
        # Sort tools by priority
        tools_to_use = sorted(
            tool_decisions.get('tools_to_use', []),
            key=lambda x: x.get('priority', 0),
            reverse=True
        )
        
        # Execute tools in priority order, respecting dependencies
        for tool_spec in tools_to_use:
            tool_name = tool_spec.get('tool_name')
            
            if not tool_name or tool_name in executed_tools:
                continue
            
            # Check dependencies
            dependencies = self._get_tool_dependencies(tool_name, tool_decisions.get('tool_chain', []))
            if not all(dep in executed_tools for dep in dependencies):
                logger.info(f"Skipping {tool_name} - dependencies not met: {dependencies}")
                continue
            
            # Execute the tool with retry logic
            logger.info(f"Executing tool: {tool_name}")

            tool_result = self._execute_tool_with_retry(
                tool_name=tool_name,
                tool_spec=tool_spec,
                filing_entry=filing_entry,
                results=results
            )

            # Store result
            results[tool_name] = tool_result

            # Mark as executed if successful
            if "error" not in tool_result:
                executed_tools.add(tool_name)
                logger.info(f"Successfully executed {tool_name} in {tool_result.get('duration_seconds', 0):.2f}s")
            else:
                logger.error(f"Failed to execute {tool_name}: {tool_result.get('error', 'Unknown error')}")
        
        return results

    def _compress_previous_results(self, previous_results: Dict[str, Any]) -> str:
        """
        Compress previous results to essential information only.

        Args:
            previous_results: Previous analysis results

        Returns:
            Compressed summary string
        """
        if not previous_results:
            return "None"

        # Extract only key metrics and scores
        summary_parts = []

        if 'relevance_score' in previous_results:
            summary_parts.append(f"Relevance: {previous_results['relevance_score']:.2f}")

        if 'risk_level' in previous_results:
            summary_parts.append(f"Risk: {previous_results['risk_level']}")

        if 'industry_analysis' in previous_results:
            industry = previous_results['industry_analysis']
            if isinstance(industry, dict) and 'sector' in industry:
                summary_parts.append(f"Sector: {industry['sector']}")

        if 'key_findings' in previous_results:
            findings = previous_results['key_findings']
            if isinstance(findings, list) and findings:
                summary_parts.append(f"Key Finding: {findings[0][:50]}...")

        return " | ".join(summary_parts) if summary_parts else "No significant findings"

    def _format_filing_context(self, filing_entry: Dict[str, Any]) -> str:
        """Format filing entry for prompt inclusion."""
        return f"""
Title: {filing_entry.get('title', 'Unknown')}
Issuer: {filing_entry.get('issuer_name', 'Unknown')}
Industry: {filing_entry.get('industry_group', 'Unknown')}
Offering Amount: ${filing_entry.get('offering_amount', 0):,.2f}
Filing Date: {filing_entry.get('filing_date', 'Unknown')}
Description: {filing_entry.get('description', 'No description available')[:200]}...
"""
    
    def _parse_tool_selection(self, llm_output: str) -> Dict[str, Any]:
        """Parse LLM output for tool selection decisions."""
        try:
            # Try to extract JSON from the output
            json_match = re.search(r'```json\s*(.*?)\s*```', llm_output, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_match = re.search(r'({.*})', llm_output, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                else:
                    json_str = llm_output
            
            parsed = json.loads(json_str)
            
            # Validate structure
            if not isinstance(parsed.get('tools_to_use'), list):
                parsed['tools_to_use'] = []
            
            if not isinstance(parsed.get('tool_chain'), list):
                parsed['tool_chain'] = []
            
            return parsed
            
        except Exception as e:
            logger.error(f"Error parsing tool selection: {e}")
            return {
                "tools_to_use": [],
                "tool_chain": [],
                "reasoning": f"Failed to parse LLM output: {str(e)}"
            }
    
    def _get_tool_dependencies(self, tool_name: str, tool_chain: List[Dict[str, Any]]) -> List[str]:
        """Get dependencies for a specific tool from the tool chain."""
        for step in tool_chain:
            if step.get('tool_name') == tool_name:
                return step.get('depends_on', [])
        return []
    
    def _enhance_tool_parameters(self, 
                                base_params: Dict[str, Any],
                                filing_entry: Dict[str, Any],
                                previous_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance tool parameters with context from filing and previous results.
        
        Args:
            base_params: Base parameters from LLM decision
            filing_entry: Current filing entry
            previous_results: Results from previously executed tools
            
        Returns:
            Enhanced parameters
        """
        enhanced = base_params.copy()
        
        # Add common context parameters
        if 'query' in enhanced and not enhanced['query']:
            # Auto-generate query for news scraper
            company_name = filing_entry.get('issuer_name', '')
            industry = filing_entry.get('industry_group', '')
            enhanced['query'] = f"{company_name} {industry} funding investment"
        
        # Add filing-specific parameters
        enhanced['_filing_context'] = {
            'issuer_name': filing_entry.get('issuer_name'),
            'industry': filing_entry.get('industry_group'),
            'amount': filing_entry.get('offering_amount'),
            'filing_date': filing_entry.get('filing_date')
        }
        
        return enhanced
