#!/usr/bin/env python3
"""
Optimized Prompt Manager for SEC Form D Analysis

Implements token-efficient prompt generation with:
- Critical field prioritization
- Intelligent content compression
- Real-time token counting
- Context relevance preservation
"""

import json
import logging
import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)

class OptimizedPromptManager:
    """
    Optimized prompt manager that reduces token usage while preserving analysis quality.
    """
    
    def __init__(self, max_tokens: int = 8000, compression_ratio: float = 0.5):
        """
        Initialize the optimized prompt manager.
        
        Args:
            max_tokens: Maximum tokens allowed in prompts
            compression_ratio: Target compression ratio (0.5 = 50% reduction)
        """
        self.max_tokens = max_tokens
        self.compression_ratio = compression_ratio
        
        # Critical Form D fields (prioritized for inclusion)
        self.critical_fields = {
            'issuer_name': 10,      # Highest priority
            'offering_amount': 9,
            'industry_group': 8,
            'filing_date': 7,
            'issuer_city': 6,
            'issuer_state': 6,
            'exemption_type': 5,
            'minimum_investment': 4,
            'total_offering_amount': 4,
            'description': 3,       # Lower priority due to length
            'title': 2
        }
        
        # Token estimation (rough approximation: 1 token ≈ 4 characters)
        self.chars_per_token = 4
        
        # Performance tracking
        self.compression_stats = {
            "prompts_generated": 0,
            "total_original_tokens": 0,
            "total_compressed_tokens": 0,
            "avg_compression_ratio": 0.0
        }
    
    def estimate_tokens(self, text: str) -> int:
        """
        Estimate token count for text.
        
        Args:
            text: Text to estimate tokens for
            
        Returns:
            Estimated token count
        """
        return len(text) // self.chars_per_token
    
    def prioritize_filing_fields(self, filing_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prioritize and filter filing fields based on importance.
        
        Args:
            filing_data: Raw filing data
            
        Returns:
            Prioritized and filtered filing data
        """
        prioritized = {}
        
        # Sort fields by priority
        for field, priority in sorted(self.critical_fields.items(), 
                                    key=lambda x: x[1], reverse=True):
            if field in filing_data and filing_data[field]:
                value = filing_data[field]
                
                # Compress long text fields
                if field == 'description' and isinstance(value, str):
                    value = self._compress_description(value)
                elif field == 'title' and isinstance(value, str):
                    value = value[:100] + "..." if len(value) > 100 else value
                
                prioritized[field] = value
        
        return prioritized
    
    def _compress_description(self, description: str, max_length: int = 200) -> str:
        """
        Compress filing description while preserving key information.
        
        Args:
            description: Original description
            max_length: Maximum length for compressed description
            
        Returns:
            Compressed description
        """
        if len(description) <= max_length:
            return description
        
        # Extract key financial terms and amounts
        financial_terms = re.findall(r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion|thousand))?', 
                                   description, re.IGNORECASE)
        
        # Extract key business terms
        business_terms = re.findall(r'\b(?:investment|funding|capital|equity|debt|securities|shares|technology|software|healthcare|biotech|fintech)\b', 
                                  description, re.IGNORECASE)
        
        # Take first sentence and key terms
        sentences = description.split('.')
        first_sentence = sentences[0] if sentences else description
        
        # Combine first sentence with key terms
        key_info = []
        if len(first_sentence) < max_length * 0.7:
            key_info.append(first_sentence)
        
        if financial_terms:
            key_info.append(f"Amounts: {', '.join(financial_terms[:3])}")
        
        if business_terms:
            unique_terms = list(set(term.lower() for term in business_terms))
            key_info.append(f"Sectors: {', '.join(unique_terms[:3])}")
        
        compressed = '. '.join(key_info)
        
        # Truncate if still too long
        if len(compressed) > max_length:
            compressed = compressed[:max_length-3] + "..."
        
        return compressed
    
    def compress_historical_context(self, historical_data: List[Dict[str, Any]], 
                                  max_entries: int = 3) -> str:
        """
        Compress historical filing summaries.
        
        Args:
            historical_data: List of historical filings
            max_entries: Maximum number of entries to include
            
        Returns:
            Compressed historical context string
        """
        if not historical_data:
            return "No historical context available."
        
        # Sort by relevance or date
        sorted_data = sorted(historical_data[:max_entries], 
                           key=lambda x: x.get('filing_date', ''), reverse=True)
        
        summaries = []
        for entry in sorted_data:
            summary_parts = []
            
            if entry.get('issuer_name'):
                summary_parts.append(f"Company: {entry['issuer_name'][:30]}")
            
            if entry.get('offering_amount'):
                amount = entry['offering_amount']
                if amount >= 1000000:
                    summary_parts.append(f"${amount/1000000:.1f}M")
                else:
                    summary_parts.append(f"${amount:,.0f}")
            
            if entry.get('industry_group'):
                summary_parts.append(entry['industry_group'][:20])
            
            if entry.get('filing_date'):
                summary_parts.append(entry['filing_date'][:10])
            
            summaries.append(" | ".join(summary_parts))
        
        return "\n".join(f"- {summary}" for summary in summaries)
    
    def compress_news_content(self, news_data: List[Dict[str, Any]], 
                            max_articles: int = 2) -> str:
        """
        Compress news content blocks.
        
        Args:
            news_data: List of news articles
            max_articles: Maximum number of articles to include
            
        Returns:
            Compressed news content string
        """
        if not news_data:
            return "No recent news available."
        
        compressed_articles = []
        for article in news_data[:max_articles]:
            article_parts = []
            
            if article.get('title'):
                title = article['title'][:80] + "..." if len(article['title']) > 80 else article['title']
                article_parts.append(f"Title: {title}")
            
            if article.get('summary'):
                summary = article['summary'][:120] + "..." if len(article['summary']) > 120 else article['summary']
                article_parts.append(f"Summary: {summary}")
            
            if article.get('date'):
                article_parts.append(f"Date: {article['date'][:10]}")
            
            compressed_articles.append(" | ".join(article_parts))
        
        return "\n".join(f"- {article}" for article in compressed_articles)
    
    def create_optimized_prompt(self, 
                              template: str,
                              filing_data: Dict[str, Any],
                              historical_context: Optional[List[Dict[str, Any]]] = None,
                              news_context: Optional[List[Dict[str, Any]]] = None,
                              additional_context: Optional[Dict[str, Any]] = None) -> str:
        """
        Create an optimized prompt with intelligent compression.
        
        Args:
            template: Base prompt template
            filing_data: Current filing data
            historical_context: Historical filing context
            news_context: News context
            additional_context: Additional context data
            
        Returns:
            Optimized prompt string
        """
        # Track original size
        original_size = len(template)
        
        # Prioritize and compress filing data
        prioritized_filing = self.prioritize_filing_fields(filing_data)
        filing_info = self._format_prioritized_filing(prioritized_filing)
        
        # Compress historical context
        historical_info = self.compress_historical_context(historical_context or [])
        
        # Compress news context
        news_info = self.compress_news_content(news_context or [])
        
        # Format additional context compactly
        additional_info = self._format_additional_context(additional_context or {})
        
        # Create the optimized prompt
        optimized_prompt = template.format(
            filing_info=filing_info,
            historical_context=historical_info,
            news_context=news_info,
            additional_context=additional_info
        )
        
        # Check token count and further compress if needed
        estimated_tokens = self.estimate_tokens(optimized_prompt)
        if estimated_tokens > self.max_tokens:
            optimized_prompt = self._apply_emergency_compression(optimized_prompt)
        
        # Update statistics
        self._update_compression_stats(original_size, len(optimized_prompt))
        
        return optimized_prompt
    
    def _format_prioritized_filing(self, filing_data: Dict[str, Any]) -> str:
        """Format prioritized filing data compactly."""
        info_parts = []
        
        for field, value in filing_data.items():
            if field == 'offering_amount' and isinstance(value, (int, float)):
                if value >= 1000000:
                    info_parts.append(f"Amount: ${value/1000000:.1f}M")
                else:
                    info_parts.append(f"Amount: ${value:,.0f}")
            elif field == 'issuer_name':
                info_parts.append(f"Company: {value}")
            elif field == 'industry_group':
                info_parts.append(f"Industry: {value}")
            elif field == 'filing_date':
                info_parts.append(f"Date: {value[:10]}")
            elif field in ['issuer_city', 'issuer_state']:
                if field == 'issuer_city' and 'issuer_state' in filing_data:
                    info_parts.append(f"Location: {value}, {filing_data['issuer_state']}")
            elif field not in ['issuer_state']:  # Skip state if already included with city
                info_parts.append(f"{field.replace('_', ' ').title()}: {value}")
        
        return " | ".join(info_parts)
    
    def _format_additional_context(self, context: Dict[str, Any]) -> str:
        """Format additional context compactly."""
        if not context:
            return "No additional context."
        
        context_parts = []
        for key, value in context.items():
            if isinstance(value, (str, int, float)):
                context_parts.append(f"{key}: {value}")
            elif isinstance(value, dict) and value:
                # Summarize nested dictionaries
                nested_summary = ", ".join(f"{k}: {v}" for k, v in list(value.items())[:2])
                context_parts.append(f"{key}: {nested_summary}")
        
        return " | ".join(context_parts[:3])  # Limit to 3 items
    
    def _apply_emergency_compression(self, prompt: str) -> str:
        """Apply emergency compression if prompt is still too long."""
        # Remove extra whitespace
        prompt = re.sub(r'\s+', ' ', prompt)
        
        # Truncate if still too long
        max_chars = self.max_tokens * self.chars_per_token
        if len(prompt) > max_chars:
            prompt = prompt[:max_chars-100] + "\n[Content truncated for token limit]"
        
        return prompt
    
    def _update_compression_stats(self, original_size: int, compressed_size: int):
        """Update compression statistics."""
        self.compression_stats["prompts_generated"] += 1
        
        original_tokens = original_size // self.chars_per_token
        compressed_tokens = compressed_size // self.chars_per_token
        
        self.compression_stats["total_original_tokens"] += original_tokens
        self.compression_stats["total_compressed_tokens"] += compressed_tokens
        
        # Calculate average compression ratio
        if self.compression_stats["total_original_tokens"] > 0:
            self.compression_stats["avg_compression_ratio"] = (
                1 - (self.compression_stats["total_compressed_tokens"] / 
                     self.compression_stats["total_original_tokens"])
            )
    
    def get_compression_stats(self) -> Dict[str, Any]:
        """Get compression statistics."""
        stats = self.compression_stats.copy()
        stats["token_savings"] = (
            stats["total_original_tokens"] - stats["total_compressed_tokens"]
        )
        return stats
